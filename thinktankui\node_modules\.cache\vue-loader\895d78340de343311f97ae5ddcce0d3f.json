{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751521318873}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAyNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-analysis", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 顶部操作按钮区域 -->\n    <div class=\"action-buttons\">\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleCreateAnalysis\">新建分析</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"handleExportReport\">导出报告</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-refresh\" @click=\"handleRefreshData\">刷新数据</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-setting\" @click=\"handleSettings\">分析设置</el-button>\n    </div>\n\n    <div class=\"opinion-analysis-container\">\n      <!-- 左侧导航栏 -->\n      <div class=\"left-sidebar\">\n        <div class=\"sidebar-header\">\n          <span class=\"sidebar-title\">舆情分析</span>\n          <i class=\"el-icon-data-analysis\"></i>\n        </div>\n\n        <div class=\"sidebar-search\">\n          <el-input\n            v-model=\"searchText\"\n            placeholder=\"搜索分析项目\"\n            size=\"small\"\n            prefix-icon=\"el-icon-search\"\n            @input=\"handleSearch\">\n          </el-input>\n        </div>\n\n        <div class=\"sidebar-menu\">\n          <div class=\"menu-section\">\n            <div class=\"section-title\">分析类型</div>\n            <el-menu\n              :default-active=\"activeMenuItem\"\n              class=\"sidebar-menu-list\"\n              @select=\"handleMenuSelect\">\n              <el-menu-item index=\"emotion\">\n                <i class=\"el-icon-sunny\"></i>\n                <span>情感分析</span>\n              </el-menu-item>\n              <el-menu-item index=\"trend\">\n                <i class=\"el-icon-trend-charts\"></i>\n                <span>趋势分析</span>\n              </el-menu-item>\n              <el-menu-item index=\"regional\">\n                <i class=\"el-icon-location\"></i>\n                <span>地域分析</span>\n              </el-menu-item>\n              <el-menu-item index=\"keyword\">\n                <i class=\"el-icon-key\"></i>\n                <span>关键词分析</span>\n              </el-menu-item>\n              <el-menu-item index=\"influence\">\n                <i class=\"el-icon-star-on\"></i>\n                <span>影响力评估</span>\n              </el-menu-item>\n              <el-menu-item index=\"spread\">\n                <i class=\"el-icon-share\"></i>\n                <span>传播路径</span>\n              </el-menu-item>\n            </el-menu>\n          </div>\n        </div>\n      </div>\n\n      <!-- 右侧内容区域 -->\n      <div class=\"right-content\">\n        <!-- 内容头部 -->\n        <div class=\"content-header\">\n          <div class=\"analysis-title\">\n            <span class=\"analysis-name\">{{ getCurrentAnalysisName() }}</span>\n            <i class=\"el-icon-arrow-right\"></i>\n          </div>\n          <div class=\"view-actions\">\n            <el-button-group>\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-s-grid\" :class=\"{ 'is-active': viewMode === 'grid' }\" @click=\"viewMode = 'grid'\"></el-button>\n              <el-button size=\"small\" icon=\"el-icon-menu\" :class=\"{ 'is-active': viewMode === 'list' }\" @click=\"viewMode = 'list'\"></el-button>\n              <el-button size=\"small\" icon=\"el-icon-s-unfold\" :class=\"{ 'is-active': viewMode === 'detail' }\" @click=\"viewMode = 'detail'\"></el-button>\n            </el-button-group>\n          </div>\n        </div>\n\n        <!-- 时间筛选区域 -->\n        <div class=\"filter-section\">\n          <div class=\"filter-row\">\n            <span class=\"filter-label\">时间范围:</span>\n            <el-radio-group v-model=\"timeRange\" size=\"small\">\n              <el-radio-button label=\"today\">今天</el-radio-button>\n              <el-radio-button label=\"week\">最近7天</el-radio-button>\n              <el-radio-button label=\"month\">最近30天</el-radio-button>\n              <el-radio-button label=\"custom\">自定义</el-radio-button>\n            </el-radio-group>\n\n            <el-date-picker\n              v-if=\"timeRange === 'custom'\"\n              v-model=\"customDateRange\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n              size=\"small\"\n              style=\"margin-left: 10px;\"\n              @change=\"handleDateChange\">\n            </el-date-picker>\n          </div>\n        </div>\n\n        <!-- 主要内容区域 -->\n        <div class=\"main-content\">\n          <!-- 根据选中的分析类型显示不同内容 -->\n          <div v-if=\"activeMenuItem === 'emotion'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-sunny\"></i>情感分析</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-sunny\"></i>\n                </div>\n                <h4>情感分析功能</h4>\n                <p>智能识别文本情感倾向，分析正面、负面、中性情绪分布</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n\n          <div v-else-if=\"activeMenuItem === 'trend'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-trend-charts\"></i>趋势分析</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-trend-charts\"></i>\n                </div>\n                <h4>趋势分析功能</h4>\n                <p>追踪舆情发展趋势，预测未来走向和热度变化</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n\n          <div v-else-if=\"activeMenuItem === 'regional'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-location\"></i>地域分析</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-location\"></i>\n                </div>\n                <h4>地域分析功能</h4>\n                <p>分析不同地区的舆情分布特征和地域差异</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n\n          <div v-else-if=\"activeMenuItem === 'keyword'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-key\"></i>关键词分析</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-key\"></i>\n                </div>\n                <h4>关键词分析功能</h4>\n                <p>提取热门关键词，分析词频和关联性</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n\n          <div v-else-if=\"activeMenuItem === 'influence'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-star-on\"></i>影响力评估</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-star-on\"></i>\n                </div>\n                <h4>影响力评估功能</h4>\n                <p>评估舆情事件的影响力和传播效果</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n\n          <div v-else-if=\"activeMenuItem === 'spread'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-share\"></i>传播路径</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-share\"></i>\n                </div>\n                <h4>传播路径分析功能</h4>\n                <p>追踪信息传播路径，分析传播节点和影响范围</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      // 搜索文本\n      searchText: '',\n      // 当前激活的菜单项\n      activeMenuItem: 'emotion',\n      // 视图模式\n      viewMode: 'grid',\n      // 时间范围\n      timeRange: 'week',\n      // 自定义时间范围\n      customDateRange: null,\n      // 分析类型映射\n      analysisTypeMap: {\n        'emotion': '情感分析',\n        'trend': '趋势分析',\n        'regional': '地域分析',\n        'keyword': '关键词分析',\n        'influence': '影响力评估',\n        'spread': '传播路径'\n      }\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n    this.initializeData()\n  },\n  methods: {\n    // 初始化数据\n    initializeData() {\n      // 设置默认时间范围为最近7天\n      const endDate = new Date()\n      const startDate = new Date()\n      startDate.setDate(startDate.getDate() - 7)\n      this.customDateRange = [startDate, endDate]\n    },\n\n    // 处理搜索\n    handleSearch() {\n      console.log('搜索:', this.searchText)\n      // 这里可以添加搜索逻辑\n    },\n\n    // 处理菜单选择\n    handleMenuSelect(index) {\n      this.activeMenuItem = index\n      console.log('选择分析类型:', index)\n      // 根据选择的分析类型加载对应数据\n      this.loadAnalysisData(index)\n    },\n\n    // 获取当前分析名称\n    getCurrentAnalysisName() {\n      return this.analysisTypeMap[this.activeMenuItem] || '舆情分析'\n    },\n\n    // 处理时间范围变化\n    handleDateChange() {\n      console.log('时间范围变化:', this.customDateRange)\n      this.loadAnalysisData(this.activeMenuItem)\n    },\n\n    // 加载分析数据\n    loadAnalysisData(analysisType) {\n      console.log('加载分析数据:', analysisType)\n      // 这里可以调用API获取对应的分析数据\n    },\n\n    // 顶部操作按钮事件\n    handleCreateAnalysis() {\n      this.$message.info('新建分析功能开发中')\n    },\n\n    handleExportReport() {\n      this.$message.info('导出报告功能开发中')\n    },\n\n    handleRefreshData() {\n      this.$message.success('数据刷新成功')\n      this.loadAnalysisData(this.activeMenuItem)\n    },\n\n    handleSettings() {\n      this.$message.info('分析设置功能开发中')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n// 主容器样式\n.app-container {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n// 顶部操作按钮区域\n.action-buttons {\n  background-color: #fff;\n  padding: 16px 24px;\n  border-bottom: 1px solid #e8e8e8;\n  margin-bottom: 0;\n\n  .el-button {\n    margin-right: 12px;\n\n    &:last-child {\n      margin-right: 0;\n    }\n  }\n}\n\n// 主要容器布局\n.opinion-analysis-container {\n  display: flex;\n  height: calc(100vh - 120px);\n  background-color: #f5f5f5;\n}\n\n// 左侧边栏样式\n.left-sidebar {\n  width: 280px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n\n  .sidebar-header {\n    padding: 20px 16px;\n    border-bottom: 1px solid #e8e8e8;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .sidebar-title {\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n    }\n\n    i {\n      font-size: 18px;\n      color: #666;\n    }\n  }\n\n  .sidebar-search {\n    padding: 16px;\n    border-bottom: 1px solid #f0f0f0;\n  }\n\n  .sidebar-menu {\n    flex: 1;\n    padding: 16px;\n\n    .section-title {\n      font-size: 14px;\n      color: #666;\n      margin-bottom: 12px;\n      font-weight: 500;\n    }\n\n    .sidebar-menu-list {\n      border: none;\n\n      .el-menu-item {\n        height: 44px;\n        line-height: 44px;\n        margin-bottom: 4px;\n        border-radius: 6px;\n        color: #666;\n\n        &:hover {\n          background-color: #f5f5f5;\n          color: #333;\n        }\n\n        &.is-active {\n          background-color: #e6f7ff;\n          color: #1890ff;\n\n          i {\n            color: #1890ff;\n          }\n        }\n\n        i {\n          margin-right: 8px;\n          font-size: 16px;\n        }\n      }\n    }\n  }\n}\n\n// 右侧内容区域\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #f5f5f5;\n\n  .content-header {\n    background-color: #fff;\n    padding: 16px 24px;\n    border-bottom: 1px solid #e8e8e8;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .analysis-title {\n      display: flex;\n      align-items: center;\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n\n      .analysis-name {\n        margin-right: 8px;\n      }\n\n      i {\n        color: #999;\n        font-size: 14px;\n      }\n    }\n\n    .view-actions {\n      .el-button-group {\n        .el-button {\n          &.is-active {\n            background-color: #1890ff;\n            border-color: #1890ff;\n            color: #fff;\n          }\n        }\n      }\n    }\n  }\n\n  .filter-section {\n    background-color: #fff;\n    padding: 16px 24px;\n    border-bottom: 1px solid #e8e8e8;\n\n    .filter-row {\n      display: flex;\n      align-items: center;\n      flex-wrap: wrap;\n\n      .filter-label {\n        font-size: 14px;\n        color: #666;\n        margin-right: 12px;\n        white-space: nowrap;\n      }\n\n      .el-radio-group {\n        margin-right: 16px;\n      }\n    }\n  }\n\n  .main-content {\n    flex: 1;\n    padding: 24px;\n    overflow-y: auto;\n  }\n}\n\n// 分析内容区域\n.analysis-content {\n  .content-card {\n    background-color: #fff;\n    border-radius: 8px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    overflow: hidden;\n\n    .card-header {\n      padding: 20px 24px;\n      border-bottom: 1px solid #f0f0f0;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      h3 {\n        margin: 0;\n        font-size: 16px;\n        font-weight: 600;\n        color: #333;\n        display: flex;\n        align-items: center;\n\n        i {\n          margin-right: 8px;\n          font-size: 18px;\n          color: #1890ff;\n        }\n      }\n    }\n\n    .analysis-placeholder {\n      padding: 80px 40px;\n      text-align: center;\n\n      .placeholder-icon {\n        margin-bottom: 24px;\n\n        i {\n          font-size: 64px;\n          color: #d9d9d9;\n        }\n      }\n\n      h4 {\n        font-size: 18px;\n        color: #333;\n        margin: 0 0 12px 0;\n        font-weight: 600;\n      }\n\n      p {\n        font-size: 14px;\n        color: #666;\n        margin: 0 0 32px 0;\n        line-height: 1.6;\n      }\n\n      .el-button {\n        padding: 10px 24px;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 1200px) {\n  .left-sidebar {\n    width: 240px;\n  }\n}\n\n@media (max-width: 768px) {\n  .opinion-analysis-container {\n    flex-direction: column;\n    height: auto;\n  }\n\n  .left-sidebar {\n    width: 100%;\n    height: auto;\n    border-right: none;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .right-content {\n    .main-content {\n      padding: 16px;\n    }\n  }\n\n  .action-buttons {\n    padding: 12px 16px;\n\n    .el-button {\n      margin-bottom: 8px;\n      margin-right: 8px;\n    }\n  }\n}\n</style>\n"]}]}