{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751523550972}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAsMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-analysis", "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 步骤指示器 -->\n    <div class=\"steps-container\">\n      <div class=\"step-item\" :class=\"{ active: currentStep === 1 }\">\n        <span class=\"step-number\">1</span>\n        <span class=\"step-text\">舆情分析来源</span>\n      </div>\n      <div class=\"step-item\" :class=\"{ active: currentStep === 2 }\">\n        <span class=\"step-number\">2</span>\n        <span class=\"step-text\">数据概览</span>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 第一步：分析需求 -->\n      <div v-if=\"currentStep === 1\" class=\"analysis-source\">\n        <h2 class=\"section-title\">分析需求</h2>\n\n        <!-- 实体关键词区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">实体关键词</div>\n          <el-input\n            v-model=\"entityKeyword\"\n            placeholder=\"请输入1个本次舆情分析最关注的实体词，例如你关注的品牌名称、产品名、人物名字等\"\n            class=\"entity-input\"\n          />\n        </div>\n\n        <!-- 具体需求区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">具体需求</div>\n          <el-input\n            v-model=\"specificRequirement\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请描述你在本次分析中具体关注哪些条件，例如，分析社交媒体中与拍子空间相关的消费主题及情感倾向（正面/中性/负面）\"\n            class=\"requirement-textarea\"\n          />\n        </div>\n\n        <!-- 选择关联词区域 -->\n        <!--\n        <div class=\"related-words-section\">\n          <div class=\"section-header\">\n            <span class=\"section-label\">选择关联词</span>\n            <span class=\"word-count\">(0/5)</span>\n          </div>\n\n          <div class=\"words-container\">\n            <div class=\"generate-word-btn\">\n              <i class=\"el-icon-magic-stick\"></i>\n              <span>生成关联词</span>\n            </div>\n            <div class=\"word-description\">\n              根据你填写的需求和关键词生成关联词\n            </div>\n          </div>\n        </div>\n        -->\n\n        <!-- 新的关键词选择区域 -->\n        <div class=\"keywords-selection-section\">\n          <div class=\"keywords-grid\">\n            <!-- 业绩下滑 -->\n            <div class=\"keyword-category\">\n              <div class=\"category-label\">业绩下滑</div>\n              <div class=\"keyword-tags\">\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 业绩下滑') }]\"\n                  @click=\"toggleKeyword('老板电器 业绩下滑')\"\n                >老板电器 业绩下滑</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 营收下降') }]\"\n                  @click=\"toggleKeyword('老板电器 营收下降')\"\n                >老板电器 营收下降</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 净利润下降') }]\"\n                  @click=\"toggleKeyword('老板电器 净利润下降')\"\n                >老板电器 净利润下降</el-tag>\n              </div>\n            </div>\n\n            <!-- 质量问题 -->\n            <div class=\"keyword-category\">\n              <div class=\"category-label\">质量问题</div>\n              <div class=\"keyword-tags\">\n                   <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('产品质量') }]\"\n                  @click=\"toggleKeyword('产品质量')\"\n                >产品质量</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 爆炸门') }]\"\n                  @click=\"toggleKeyword('老板电器 爆炸门')\"\n                >老板电器 爆炸门</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 投诉') }]\"\n                  @click=\"toggleKeyword('老板电器 投诉')\"\n                >老板电器 投诉</el-tag>\n              </div>\n            </div>\n\n            <!-- 股价下跌 -->\n            <div class=\"keyword-category\">\n              <div class=\"category-label\">股价下跌</div>\n              <div class=\"keyword-tags\">\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 股价下跌') }]\"\n                  @click=\"toggleKeyword('老板电器 股价下跌')\"\n                >老板电器 股价下跌</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 市值缩水') }]\"\n                  @click=\"toggleKeyword('老板电器 市值缩水')\"\n                >老板电器 市值缩水</el-tag>\n              </div>\n            </div>\n\n            <!-- 子公司亏损 -->\n            <div class=\"keyword-category\">\n              <div class=\"category-label\">子公司亏损</div>\n              <div class=\"keyword-tags\">\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 子公司亏损') }]\"\n                  @click=\"toggleKeyword('老板电器 子公司亏损')\"\n                >老板电器 子公司亏损</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 名气亏损') }]\"\n                  @click=\"toggleKeyword('老板电器 名气亏损')\"\n                >老板电器 名气亏损</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 金帝亏损') }]\"\n                  @click=\"toggleKeyword('老板电器 金帝亏损')\"\n                >老板电器 金帝亏损</el-tag>\n              </div>\n            </div>\n\n            <!-- 渠道问题 -->\n            <div class=\"keyword-category\">\n              <div class=\"category-label\">渠道问题</div>\n              <div class=\"keyword-tags\">\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 渠道冲突') }]\"\n                  @click=\"toggleKeyword('老板电器 渠道冲突')\"\n                >老板电器 渠道冲突</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 串货问题') }]\"\n                  @click=\"toggleKeyword('老板电器 串货问题')\"\n                >老板电器 串货问题</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 经销商压力') }]\"\n                  @click=\"toggleKeyword('老板电器 经销商压力')\"\n                >老板电器 经销商压力</el-tag>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 第二步：数据概览 -->\n      <div v-if=\"currentStep === 2\" class=\"data-overview\">\n        <h2 class=\"section-title\">选择数据来源</h2>\n\n        <!-- 数据来源选项 -->\n        <div class=\"data-source-section\">\n          <div class=\"source-option\" @click=\"toggleDataSource('online-search')\">\n            <el-checkbox\n              v-model=\"selectedDataSources\"\n              :value=\"'online-search'\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-search\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>联网搜索</h3>\n            </div>\n          </div>\n\n          <!-- 新增来源按钮 -->\n          <div class=\"add-source-btn\" @click=\"showAddSourceDialog\">\n            <i class=\"el-icon-plus\"></i>\n            <span>新增来源</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 底部按钮区域 -->\n      <div class=\"bottom-actions\">\n        <el-button v-if=\"currentStep === 2\" @click=\"goToPreviousStep\" size=\"large\">上一步</el-button>\n        <el-button v-if=\"currentStep === 1\" @click=\"goToNextStep\" type=\"primary\" size=\"large\">下一步</el-button>\n        <el-button v-if=\"currentStep === 2\" type=\"primary\" size=\"large\">开始分析</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      currentStep: 1, // 当前步骤\n      entityKeyword: '', // 实体关键词\n      specificRequirement: '', // 具体需求\n      selectedKeywords: [\n        '老板电器 业绩下滑',\n        '老板电器 营收下降',\n        '老板电器 净利润下降',\n        '老板电器 爆炸门'\n      ], // 已选择的关键词\n      maxKeywords: 5, // 最大选择数量\n      selectedDataSources: ['online-search'] // 已选择的数据来源\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n  },\n  methods: {\n    // 切换关键词选择状态\n    toggleKeyword(keyword) {\n      const index = this.selectedKeywords.indexOf(keyword)\n      if (index > -1) {\n        // 如果已选中，则取消选择\n        this.selectedKeywords.splice(index, 1)\n      } else {\n        // 如果未选中，检查是否超过最大数量\n        if (this.selectedKeywords.length < this.maxKeywords) {\n          this.selectedKeywords.push(keyword)\n        } else {\n          this.$message.warning(`最多只能选择${this.maxKeywords}个关键词`)\n        }\n      }\n    },\n\n    // 检查关键词是否已选中\n    isKeywordSelected(keyword) {\n      return this.selectedKeywords.includes(keyword)\n    },\n\n    // 前往下一步\n    goToNextStep() {\n      if (this.currentStep < 2) {\n        this.currentStep++\n      }\n    },\n\n    // 返回上一步\n    goToPreviousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--\n      }\n    },\n\n    // 切换数据来源选择\n    toggleDataSource(source) {\n      const index = this.selectedDataSources.indexOf(source)\n      if (index > -1) {\n        this.selectedDataSources.splice(index, 1)\n      } else {\n        this.selectedDataSources.push(source)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  padding: 0;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n// 步骤指示器样式\n.steps-container {\n  background: white;\n  padding: 20px 0;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: center;\n  gap: 60px;\n\n  .step-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: #999;\n    font-size: 14px;\n\n    &.active {\n      color: #5470c6;\n      font-weight: 500;\n\n      .step-number {\n        background: #5470c6;\n        color: white;\n      }\n    }\n\n    .step-number {\n      width: 24px;\n      height: 24px;\n      border-radius: 50%;\n      background: #e8e8e8;\n      color: #999;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 12px;\n      font-weight: 500;\n    }\n  }\n}\n\n.main-content {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 40px 24px;\n}\n\n// 分析来源区域\n.analysis-source {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n// 输入区域样式\n.input-section {\n  margin-bottom: 24px;\n\n  .input-label {\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n\n  .entity-input {\n    :deep(.el-input__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n  }\n\n  .requirement-textarea {\n    :deep(.el-textarea__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n      line-height: 1.6;\n      resize: vertical;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n  }\n}\n\n// 选择关联词区域\n.related-words-section {\n  .section-header {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    margin-bottom: 16px;\n\n    .section-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n    }\n\n    .word-count {\n      font-size: 14px;\n      color: #999;\n    }\n  }\n\n  .words-container {\n    text-align: center;\n\n    .generate-word-btn {\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      padding: 8px 16px;\n      background: #f0f7ff;\n      color: #5470c6;\n      border: 1px dashed #5470c6;\n      border-radius: 4px;\n      font-size: 14px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      margin-bottom: 12px;\n\n      &:hover {\n        background: #e6f4ff;\n        border-color: #4096ff;\n      }\n\n      i {\n        font-size: 12px;\n      }\n    }\n\n    .word-description {\n      font-size: 12px;\n      color: #999;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 关键词选择区域\n.keywords-selection-section {\n  .keywords-grid {\n    display: flex;\n    flex-direction: column;\n    gap: 20px;\n  }\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n\n    .category-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      min-width: 80px;\n      padding-top: 6px;\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n\n        &.highlight {\n          background: #333;\n          color: white;\n          border-color: #333;\n          position: relative;\n          cursor: default;\n\n          &::after {\n            content: '';\n            position: absolute;\n            bottom: -8px;\n            left: 50%;\n            transform: translateX(-50%);\n            width: 0;\n            height: 0;\n            border-left: 6px solid transparent;\n            border-right: 6px solid transparent;\n            border-top: 6px solid #333;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 第二步：数据概览样式\n.data-overview {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n.data-source-section {\n  .source-option {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n    padding: 16px;\n    border: 1px solid #e8e8e8;\n    border-radius: 8px;\n    margin-bottom: 16px;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    .source-checkbox {\n      :deep(.el-checkbox__input) {\n        .el-checkbox__inner {\n          width: 18px;\n          height: 18px;\n          border-radius: 4px;\n          border: 2px solid #d9d9d9;\n\n          &::after {\n            width: 5px;\n            height: 9px;\n            left: 5px;\n            top: 1px;\n          }\n        }\n\n        &.is-checked .el-checkbox__inner {\n          background-color: #5470c6;\n          border-color: #5470c6;\n        }\n      }\n    }\n\n    .source-icon {\n      width: 40px;\n      height: 40px;\n      background: #f0f7ff;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      i {\n        font-size: 20px;\n        color: #5470c6;\n      }\n    }\n\n    .source-content {\n      h3 {\n        font-size: 16px;\n        font-weight: 500;\n        color: #333;\n        margin: 0;\n      }\n    }\n  }\n\n  .add-source-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    padding: 16px;\n    border: 2px dashed #d9d9d9;\n    border-radius: 8px;\n    color: #999;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    font-size: 14px;\n\n    &:hover {\n      border-color: #5470c6;\n      color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    i {\n      font-size: 16px;\n    }\n  }\n}\n\n// 底部按钮区域\n.bottom-actions {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 16px;\n  padding-top: 24px;\n\n  .el-button {\n    padding: 12px 32px;\n    font-size: 16px;\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .steps-container {\n    gap: 30px;\n    padding: 16px 0;\n\n    .step-item {\n      font-size: 13px;\n    }\n  }\n\n  .main-content {\n    padding: 24px 16px;\n  }\n\n  .analysis-source {\n    padding: 24px 20px;\n  }\n\n  .document-content {\n    padding: 12px;\n    min-height: 100px;\n  }\n}\n</style>\n"]}]}