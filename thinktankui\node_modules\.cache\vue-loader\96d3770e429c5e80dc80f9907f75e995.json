{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=template&id=040a21b8&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751521318873}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750933731210}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}