{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=style&index=0&id=040a21b8&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751523193846}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750933728705}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750933731152}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750933729640}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750933728031}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA6PA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-analysis", "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 步骤指示器 -->\n    <div class=\"steps-container\">\n      <div class=\"step-item\" :class=\"{ active: currentStep === 1 }\">\n        <span class=\"step-number\">1</span>\n        <span class=\"step-text\">舆情分析来源</span>\n      </div>\n      <div class=\"step-item\" :class=\"{ active: currentStep === 2 }\">\n        <span class=\"step-number\">2</span>\n        <span class=\"step-text\">数据概览</span>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 第一步：分析需求 -->\n      <div v-if=\"currentStep === 1\" class=\"analysis-source\">\n        <h2 class=\"section-title\">分析需求</h2>\n\n        <!-- 实体关键词区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">实体关键词</div>\n          <el-input\n            v-model=\"entityKeyword\"\n            placeholder=\"请输入1个本次舆情分析最关注的实体词，例如你关注的品牌名称、产品名、人物名字等\"\n            class=\"entity-input\"\n          />\n        </div>\n\n        <!-- 具体需求区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">具体需求</div>\n          <el-input\n            v-model=\"specificRequirement\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请描述你在本次分析中具体关注哪些条件，例如，分析社交媒体中与拍子空间相关的消费主题及情感倾向（正面/中性/负面）\"\n            class=\"requirement-textarea\"\n          />\n        </div>\n\n        <!-- 选择关联词区域 -->\n        <!--\n        <div class=\"related-words-section\">\n          <div class=\"section-header\">\n            <span class=\"section-label\">选择关联词</span>\n            <span class=\"word-count\">(0/5)</span>\n          </div>\n\n          <div class=\"words-container\">\n            <div class=\"generate-word-btn\">\n              <i class=\"el-icon-magic-stick\"></i>\n              <span>生成关联词</span>\n            </div>\n            <div class=\"word-description\">\n              根据你填写的需求和关键词生成关联词\n            </div>\n          </div>\n        </div>\n        -->\n\n        <!-- 新的关键词选择区域 -->\n        <div class=\"keywords-selection-section\">\n          <div class=\"keywords-grid\">\n            <!-- 业绩下滑 -->\n            <div class=\"keyword-category\">\n              <div class=\"category-label\">业绩下滑</div>\n              <div class=\"keyword-tags\">\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 业绩下滑') }]\"\n                  @click=\"toggleKeyword('老板电器 业绩下滑')\"\n                >老板电器 业绩下滑</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 营收下降') }]\"\n                  @click=\"toggleKeyword('老板电器 营收下降')\"\n                >老板电器 营收下降</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 净利润下降') }]\"\n                  @click=\"toggleKeyword('老板电器 净利润下降')\"\n                >老板电器 净利润下降</el-tag>\n              </div>\n            </div>\n\n            <!-- 质量问题 -->\n            <div class=\"keyword-category\">\n              <div class=\"category-label\">质量问题</div>\n              <div class=\"keyword-tags\">\n                   <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('产品质量') }]\"\n                  @click=\"toggleKeyword('产品质量')\"\n                >产品质量</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 爆炸门') }]\"\n                  @click=\"toggleKeyword('老板电器 爆炸门')\"\n                >老板电器 爆炸门</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 投诉') }]\"\n                  @click=\"toggleKeyword('老板电器 投诉')\"\n                >老板电器 投诉</el-tag>\n              </div>\n            </div>\n\n            <!-- 股价下跌 -->\n            <div class=\"keyword-category\">\n              <div class=\"category-label\">股价下跌</div>\n              <div class=\"keyword-tags\">\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 股价下跌') }]\"\n                  @click=\"toggleKeyword('老板电器 股价下跌')\"\n                >老板电器 股价下跌</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 市值缩水') }]\"\n                  @click=\"toggleKeyword('老板电器 市值缩水')\"\n                >老板电器 市值缩水</el-tag>\n              </div>\n            </div>\n\n            <!-- 子公司亏损 -->\n            <div class=\"keyword-category\">\n              <div class=\"category-label\">子公司亏损</div>\n              <div class=\"keyword-tags\">\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 子公司亏损') }]\"\n                  @click=\"toggleKeyword('老板电器 子公司亏损')\"\n                >老板电器 子公司亏损</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 名气亏损') }]\"\n                  @click=\"toggleKeyword('老板电器 名气亏损')\"\n                >老板电器 名气亏损</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 金帝亏损') }]\"\n                  @click=\"toggleKeyword('老板电器 金帝亏损')\"\n                >老板电器 金帝亏损</el-tag>\n              </div>\n            </div>\n\n            <!-- 渠道问题 -->\n            <div class=\"keyword-category\">\n              <div class=\"category-label\">渠道问题</div>\n              <div class=\"keyword-tags\">\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 渠道冲突') }]\"\n                  @click=\"toggleKeyword('老板电器 渠道冲突')\"\n                >老板电器 渠道冲突</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 串货问题') }]\"\n                  @click=\"toggleKeyword('老板电器 串货问题')\"\n                >老板电器 串货问题</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 经销商压力') }]\"\n                  @click=\"toggleKeyword('老板电器 经销商压力')\"\n                >老板电器 经销商压力</el-tag>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 第二步：数据概览 -->\n      <div v-if=\"currentStep === 2\" class=\"data-overview\">\n        <h2 class=\"section-title\">选择数据来源</h2>\n\n        <!-- 数据来源选项 -->\n        <div class=\"data-source-section\">\n          <div class=\"source-option\">\n            <div class=\"source-icon\">\n              <i class=\"el-icon-search\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>联网搜索</h3>\n            </div>\n          </div>\n\n          <!-- 新增来源按钮 -->\n          <div class=\"add-source-btn\">\n            <i class=\"el-icon-plus\"></i>\n            <span>新增来源</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 底部按钮区域 -->\n      <div class=\"bottom-actions\">\n        <el-button v-if=\"currentStep === 2\" @click=\"goToPreviousStep\" size=\"large\">上一步</el-button>\n        <el-button v-if=\"currentStep === 1\" @click=\"goToNextStep\" type=\"primary\" size=\"large\">下一步</el-button>\n        <el-button v-if=\"currentStep === 2\" type=\"primary\" size=\"large\">开始分析</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      currentStep: 1, // 当前步骤\n      entityKeyword: '', // 实体关键词\n      specificRequirement: '', // 具体需求\n      selectedKeywords: [\n        '老板电器 业绩下滑',\n        '老板电器 营收下降',\n        '老板电器 净利润下降',\n        '老板电器 爆炸门'\n      ], // 已选择的关键词\n      maxKeywords: 5 // 最大选择数量\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n  },\n  methods: {\n    // 切换关键词选择状态\n    toggleKeyword(keyword) {\n      const index = this.selectedKeywords.indexOf(keyword)\n      if (index > -1) {\n        // 如果已选中，则取消选择\n        this.selectedKeywords.splice(index, 1)\n      } else {\n        // 如果未选中，检查是否超过最大数量\n        if (this.selectedKeywords.length < this.maxKeywords) {\n          this.selectedKeywords.push(keyword)\n        } else {\n          this.$message.warning(`最多只能选择${this.maxKeywords}个关键词`)\n        }\n      }\n    },\n\n    // 检查关键词是否已选中\n    isKeywordSelected(keyword) {\n      return this.selectedKeywords.includes(keyword)\n    },\n\n    // 前往下一步\n    goToNextStep() {\n      if (this.currentStep < 2) {\n        this.currentStep++\n      }\n    },\n\n    // 返回上一步\n    goToPreviousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  padding: 0;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n// 步骤指示器样式\n.steps-container {\n  background: white;\n  padding: 20px 0;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: center;\n  gap: 60px;\n\n  .step-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: #999;\n    font-size: 14px;\n\n    &.active {\n      color: #5470c6;\n      font-weight: 500;\n\n      .step-number {\n        background: #5470c6;\n        color: white;\n      }\n    }\n\n    .step-number {\n      width: 24px;\n      height: 24px;\n      border-radius: 50%;\n      background: #e8e8e8;\n      color: #999;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 12px;\n      font-weight: 500;\n    }\n  }\n}\n\n.main-content {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 40px 24px;\n}\n\n// 分析来源区域\n.analysis-source {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n// 输入区域样式\n.input-section {\n  margin-bottom: 24px;\n\n  .input-label {\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n\n  .entity-input {\n    :deep(.el-input__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n  }\n\n  .requirement-textarea {\n    :deep(.el-textarea__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n      line-height: 1.6;\n      resize: vertical;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n  }\n}\n\n// 选择关联词区域\n.related-words-section {\n  .section-header {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    margin-bottom: 16px;\n\n    .section-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n    }\n\n    .word-count {\n      font-size: 14px;\n      color: #999;\n    }\n  }\n\n  .words-container {\n    text-align: center;\n\n    .generate-word-btn {\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      padding: 8px 16px;\n      background: #f0f7ff;\n      color: #5470c6;\n      border: 1px dashed #5470c6;\n      border-radius: 4px;\n      font-size: 14px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      margin-bottom: 12px;\n\n      &:hover {\n        background: #e6f4ff;\n        border-color: #4096ff;\n      }\n\n      i {\n        font-size: 12px;\n      }\n    }\n\n    .word-description {\n      font-size: 12px;\n      color: #999;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 关键词选择区域\n.keywords-selection-section {\n  .keywords-grid {\n    display: flex;\n    flex-direction: column;\n    gap: 20px;\n  }\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n\n    .category-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      min-width: 80px;\n      padding-top: 6px;\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n\n        &.highlight {\n          background: #333;\n          color: white;\n          border-color: #333;\n          position: relative;\n          cursor: default;\n\n          &::after {\n            content: '';\n            position: absolute;\n            bottom: -8px;\n            left: 50%;\n            transform: translateX(-50%);\n            width: 0;\n            height: 0;\n            border-left: 6px solid transparent;\n            border-right: 6px solid transparent;\n            border-top: 6px solid #333;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 第二步：数据概览样式\n.data-overview {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n.data-source-section {\n  .source-option {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n    padding: 16px;\n    border: 1px solid #e8e8e8;\n    border-radius: 8px;\n    margin-bottom: 16px;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    .source-icon {\n      width: 40px;\n      height: 40px;\n      background: #f0f7ff;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      i {\n        font-size: 20px;\n        color: #5470c6;\n      }\n    }\n\n    .source-content {\n      h3 {\n        font-size: 16px;\n        font-weight: 500;\n        color: #333;\n        margin: 0;\n      }\n    }\n  }\n\n  .add-source-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    padding: 16px;\n    border: 2px dashed #d9d9d9;\n    border-radius: 8px;\n    color: #999;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    font-size: 14px;\n\n    &:hover {\n      border-color: #5470c6;\n      color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    i {\n      font-size: 16px;\n    }\n  }\n}\n\n// 底部按钮区域\n.bottom-actions {\n  text-align: center;\n  padding-top: 24px;\n\n  .el-button {\n    padding: 12px 32px;\n    font-size: 16px;\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .steps-container {\n    gap: 30px;\n    padding: 16px 0;\n\n    .step-item {\n      font-size: 13px;\n    }\n  }\n\n  .main-content {\n    padding: 24px 16px;\n  }\n\n  .analysis-source {\n    padding: 24px 20px;\n  }\n\n  .document-content {\n    padding: 12px;\n    min-height: 100px;\n  }\n}\n</style>\n"]}]}