{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=style&index=0&id=040a21b8&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751521318873}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750933728705}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750933731152}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750933729640}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750933728031}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAsTA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-analysis", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 顶部操作按钮区域 -->\n    <div class=\"action-buttons\">\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleCreateAnalysis\">新建分析</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"handleExportReport\">导出报告</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-refresh\" @click=\"handleRefreshData\">刷新数据</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-setting\" @click=\"handleSettings\">分析设置</el-button>\n    </div>\n\n    <div class=\"opinion-analysis-container\">\n      <!-- 左侧导航栏 -->\n      <div class=\"left-sidebar\">\n        <div class=\"sidebar-header\">\n          <span class=\"sidebar-title\">舆情分析</span>\n          <i class=\"el-icon-data-analysis\"></i>\n        </div>\n\n        <div class=\"sidebar-search\">\n          <el-input\n            v-model=\"searchText\"\n            placeholder=\"搜索分析项目\"\n            size=\"small\"\n            prefix-icon=\"el-icon-search\"\n            @input=\"handleSearch\">\n          </el-input>\n        </div>\n\n        <div class=\"sidebar-menu\">\n          <div class=\"menu-section\">\n            <div class=\"section-title\">分析类型</div>\n            <el-menu\n              :default-active=\"activeMenuItem\"\n              class=\"sidebar-menu-list\"\n              @select=\"handleMenuSelect\">\n              <el-menu-item index=\"emotion\">\n                <i class=\"el-icon-sunny\"></i>\n                <span>情感分析</span>\n              </el-menu-item>\n              <el-menu-item index=\"trend\">\n                <i class=\"el-icon-trend-charts\"></i>\n                <span>趋势分析</span>\n              </el-menu-item>\n              <el-menu-item index=\"regional\">\n                <i class=\"el-icon-location\"></i>\n                <span>地域分析</span>\n              </el-menu-item>\n              <el-menu-item index=\"keyword\">\n                <i class=\"el-icon-key\"></i>\n                <span>关键词分析</span>\n              </el-menu-item>\n              <el-menu-item index=\"influence\">\n                <i class=\"el-icon-star-on\"></i>\n                <span>影响力评估</span>\n              </el-menu-item>\n              <el-menu-item index=\"spread\">\n                <i class=\"el-icon-share\"></i>\n                <span>传播路径</span>\n              </el-menu-item>\n            </el-menu>\n          </div>\n        </div>\n      </div>\n\n      <!-- 右侧内容区域 -->\n      <div class=\"right-content\">\n        <!-- 内容头部 -->\n        <div class=\"content-header\">\n          <div class=\"analysis-title\">\n            <span class=\"analysis-name\">{{ getCurrentAnalysisName() }}</span>\n            <i class=\"el-icon-arrow-right\"></i>\n          </div>\n          <div class=\"view-actions\">\n            <el-button-group>\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-s-grid\" :class=\"{ 'is-active': viewMode === 'grid' }\" @click=\"viewMode = 'grid'\"></el-button>\n              <el-button size=\"small\" icon=\"el-icon-menu\" :class=\"{ 'is-active': viewMode === 'list' }\" @click=\"viewMode = 'list'\"></el-button>\n              <el-button size=\"small\" icon=\"el-icon-s-unfold\" :class=\"{ 'is-active': viewMode === 'detail' }\" @click=\"viewMode = 'detail'\"></el-button>\n            </el-button-group>\n          </div>\n        </div>\n\n        <!-- 时间筛选区域 -->\n        <div class=\"filter-section\">\n          <div class=\"filter-row\">\n            <span class=\"filter-label\">时间范围:</span>\n            <el-radio-group v-model=\"timeRange\" size=\"small\">\n              <el-radio-button label=\"today\">今天</el-radio-button>\n              <el-radio-button label=\"week\">最近7天</el-radio-button>\n              <el-radio-button label=\"month\">最近30天</el-radio-button>\n              <el-radio-button label=\"custom\">自定义</el-radio-button>\n            </el-radio-group>\n\n            <el-date-picker\n              v-if=\"timeRange === 'custom'\"\n              v-model=\"customDateRange\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n              size=\"small\"\n              style=\"margin-left: 10px;\"\n              @change=\"handleDateChange\">\n            </el-date-picker>\n          </div>\n        </div>\n\n        <!-- 主要内容区域 -->\n        <div class=\"main-content\">\n          <!-- 根据选中的分析类型显示不同内容 -->\n          <div v-if=\"activeMenuItem === 'emotion'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-sunny\"></i>情感分析</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-sunny\"></i>\n                </div>\n                <h4>情感分析功能</h4>\n                <p>智能识别文本情感倾向，分析正面、负面、中性情绪分布</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n\n          <div v-else-if=\"activeMenuItem === 'trend'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-trend-charts\"></i>趋势分析</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-trend-charts\"></i>\n                </div>\n                <h4>趋势分析功能</h4>\n                <p>追踪舆情发展趋势，预测未来走向和热度变化</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n\n          <div v-else-if=\"activeMenuItem === 'regional'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-location\"></i>地域分析</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-location\"></i>\n                </div>\n                <h4>地域分析功能</h4>\n                <p>分析不同地区的舆情分布特征和地域差异</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n\n          <div v-else-if=\"activeMenuItem === 'keyword'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-key\"></i>关键词分析</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-key\"></i>\n                </div>\n                <h4>关键词分析功能</h4>\n                <p>提取热门关键词，分析词频和关联性</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n\n          <div v-else-if=\"activeMenuItem === 'influence'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-star-on\"></i>影响力评估</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-star-on\"></i>\n                </div>\n                <h4>影响力评估功能</h4>\n                <p>评估舆情事件的影响力和传播效果</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n\n          <div v-else-if=\"activeMenuItem === 'spread'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-share\"></i>传播路径</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-share\"></i>\n                </div>\n                <h4>传播路径分析功能</h4>\n                <p>追踪信息传播路径，分析传播节点和影响范围</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      // 搜索文本\n      searchText: '',\n      // 当前激活的菜单项\n      activeMenuItem: 'emotion',\n      // 视图模式\n      viewMode: 'grid',\n      // 时间范围\n      timeRange: 'week',\n      // 自定义时间范围\n      customDateRange: null,\n      // 分析类型映射\n      analysisTypeMap: {\n        'emotion': '情感分析',\n        'trend': '趋势分析',\n        'regional': '地域分析',\n        'keyword': '关键词分析',\n        'influence': '影响力评估',\n        'spread': '传播路径'\n      }\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n    this.initializeData()\n  },\n  methods: {\n    // 初始化数据\n    initializeData() {\n      // 设置默认时间范围为最近7天\n      const endDate = new Date()\n      const startDate = new Date()\n      startDate.setDate(startDate.getDate() - 7)\n      this.customDateRange = [startDate, endDate]\n    },\n\n    // 处理搜索\n    handleSearch() {\n      console.log('搜索:', this.searchText)\n      // 这里可以添加搜索逻辑\n    },\n\n    // 处理菜单选择\n    handleMenuSelect(index) {\n      this.activeMenuItem = index\n      console.log('选择分析类型:', index)\n      // 根据选择的分析类型加载对应数据\n      this.loadAnalysisData(index)\n    },\n\n    // 获取当前分析名称\n    getCurrentAnalysisName() {\n      return this.analysisTypeMap[this.activeMenuItem] || '舆情分析'\n    },\n\n    // 处理时间范围变化\n    handleDateChange() {\n      console.log('时间范围变化:', this.customDateRange)\n      this.loadAnalysisData(this.activeMenuItem)\n    },\n\n    // 加载分析数据\n    loadAnalysisData(analysisType) {\n      console.log('加载分析数据:', analysisType)\n      // 这里可以调用API获取对应的分析数据\n    },\n\n    // 顶部操作按钮事件\n    handleCreateAnalysis() {\n      this.$message.info('新建分析功能开发中')\n    },\n\n    handleExportReport() {\n      this.$message.info('导出报告功能开发中')\n    },\n\n    handleRefreshData() {\n      this.$message.success('数据刷新成功')\n      this.loadAnalysisData(this.activeMenuItem)\n    },\n\n    handleSettings() {\n      this.$message.info('分析设置功能开发中')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n// 主容器样式\n.app-container {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n// 顶部操作按钮区域\n.action-buttons {\n  background-color: #fff;\n  padding: 16px 24px;\n  border-bottom: 1px solid #e8e8e8;\n  margin-bottom: 0;\n\n  .el-button {\n    margin-right: 12px;\n\n    &:last-child {\n      margin-right: 0;\n    }\n  }\n}\n\n// 主要容器布局\n.opinion-analysis-container {\n  display: flex;\n  height: calc(100vh - 120px);\n  background-color: #f5f5f5;\n}\n\n// 左侧边栏样式\n.left-sidebar {\n  width: 280px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n\n  .sidebar-header {\n    padding: 20px 16px;\n    border-bottom: 1px solid #e8e8e8;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .sidebar-title {\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n    }\n\n    i {\n      font-size: 18px;\n      color: #666;\n    }\n  }\n\n  .sidebar-search {\n    padding: 16px;\n    border-bottom: 1px solid #f0f0f0;\n  }\n\n  .sidebar-menu {\n    flex: 1;\n    padding: 16px;\n\n    .section-title {\n      font-size: 14px;\n      color: #666;\n      margin-bottom: 12px;\n      font-weight: 500;\n    }\n\n    .sidebar-menu-list {\n      border: none;\n\n      .el-menu-item {\n        height: 44px;\n        line-height: 44px;\n        margin-bottom: 4px;\n        border-radius: 6px;\n        color: #666;\n\n        &:hover {\n          background-color: #f5f5f5;\n          color: #333;\n        }\n\n        &.is-active {\n          background-color: #e6f7ff;\n          color: #1890ff;\n\n          i {\n            color: #1890ff;\n          }\n        }\n\n        i {\n          margin-right: 8px;\n          font-size: 16px;\n        }\n      }\n    }\n  }\n}\n\n// 右侧内容区域\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #f5f5f5;\n\n  .content-header {\n    background-color: #fff;\n    padding: 16px 24px;\n    border-bottom: 1px solid #e8e8e8;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .analysis-title {\n      display: flex;\n      align-items: center;\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n\n      .analysis-name {\n        margin-right: 8px;\n      }\n\n      i {\n        color: #999;\n        font-size: 14px;\n      }\n    }\n\n    .view-actions {\n      .el-button-group {\n        .el-button {\n          &.is-active {\n            background-color: #1890ff;\n            border-color: #1890ff;\n            color: #fff;\n          }\n        }\n      }\n    }\n  }\n\n  .filter-section {\n    background-color: #fff;\n    padding: 16px 24px;\n    border-bottom: 1px solid #e8e8e8;\n\n    .filter-row {\n      display: flex;\n      align-items: center;\n      flex-wrap: wrap;\n\n      .filter-label {\n        font-size: 14px;\n        color: #666;\n        margin-right: 12px;\n        white-space: nowrap;\n      }\n\n      .el-radio-group {\n        margin-right: 16px;\n      }\n    }\n  }\n\n  .main-content {\n    flex: 1;\n    padding: 24px;\n    overflow-y: auto;\n  }\n}\n\n// 分析内容区域\n.analysis-content {\n  .content-card {\n    background-color: #fff;\n    border-radius: 8px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    overflow: hidden;\n\n    .card-header {\n      padding: 20px 24px;\n      border-bottom: 1px solid #f0f0f0;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      h3 {\n        margin: 0;\n        font-size: 16px;\n        font-weight: 600;\n        color: #333;\n        display: flex;\n        align-items: center;\n\n        i {\n          margin-right: 8px;\n          font-size: 18px;\n          color: #1890ff;\n        }\n      }\n    }\n\n    .analysis-placeholder {\n      padding: 80px 40px;\n      text-align: center;\n\n      .placeholder-icon {\n        margin-bottom: 24px;\n\n        i {\n          font-size: 64px;\n          color: #d9d9d9;\n        }\n      }\n\n      h4 {\n        font-size: 18px;\n        color: #333;\n        margin: 0 0 12px 0;\n        font-weight: 600;\n      }\n\n      p {\n        font-size: 14px;\n        color: #666;\n        margin: 0 0 32px 0;\n        line-height: 1.6;\n      }\n\n      .el-button {\n        padding: 10px 24px;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 1200px) {\n  .left-sidebar {\n    width: 240px;\n  }\n}\n\n@media (max-width: 768px) {\n  .opinion-analysis-container {\n    flex-direction: column;\n    height: auto;\n  }\n\n  .left-sidebar {\n    width: 100%;\n    height: auto;\n    border-right: none;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .right-content {\n    .main-content {\n      padding: 16px;\n    }\n  }\n\n  .action-buttons {\n    padding: 12px 16px;\n\n    .el-button {\n      margin-bottom: 8px;\n      margin-right: 8px;\n    }\n  }\n}\n</style>\n"]}]}