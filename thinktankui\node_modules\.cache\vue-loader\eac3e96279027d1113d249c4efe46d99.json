{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=style&index=0&id=040a21b8&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751521567988}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750933728705}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750933731152}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750933729640}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750933728031}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA+EA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-analysis", "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 步骤指示器 -->\n    <div class=\"steps-container\">\n      <div class=\"step-item active\">\n        <span class=\"step-number\">1</span>\n        <span class=\"step-text\">舆情分析来源</span>\n      </div>\n      <div class=\"step-item\">\n        <span class=\"step-number\">2</span>\n        <span class=\"step-text\">数据概览</span>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 分析来源区域 -->\n      <div class=\"analysis-source\">\n        <h2 class=\"section-title\">分析需求</h2>\n\n        <!-- 立体文档区域 -->\n        <div class=\"document-section\">\n          <div class=\"document-label\">立体文档</div>\n          <div class=\"document-content\">\n            <div class=\"document-text\">\n              中国人工智能的发展历程可以分为四个阶段，即萌芽期、发展期、产业化、人工智能等。\n            </div>\n            <div class=\"document-text\">\n              当前正在快速发展阶段中国人工智能产业，例如，分析已突破传统行业的应用边界，在金融、医疗等传统领域（工业/制造业）\n            </div>\n          </div>\n        </div>\n\n        <!-- 选择关联词区域 -->\n        <div class=\"related-words-section\">\n          <div class=\"section-header\">\n            <span class=\"section-label\">选择关联词</span>\n            <span class=\"word-count\">(0/)</span>\n          </div>\n\n          <div class=\"words-container\">\n            <div class=\"add-word-btn\">\n              <i class=\"el-icon-plus\"></i>\n              <span>立体文档</span>\n            </div>\n            <div class=\"word-description\">\n              将根据选定的关联词对上述工作进行立体文档\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 底部按钮区域 -->\n      <div class=\"bottom-actions\">\n        <el-button type=\"primary\" size=\"large\">下一步</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      // 页面数据将在这里定义\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n  },\n  methods: {\n    // 页面方法将在这里定义\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  padding: 0;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n// 步骤指示器样式\n.steps-container {\n  background: white;\n  padding: 20px 0;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: center;\n  gap: 60px;\n\n  .step-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: #999;\n    font-size: 14px;\n\n    &.active {\n      color: #5470c6;\n      font-weight: 500;\n\n      .step-number {\n        background: #5470c6;\n        color: white;\n      }\n    }\n\n    .step-number {\n      width: 24px;\n      height: 24px;\n      border-radius: 50%;\n      background: #e8e8e8;\n      color: #999;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 12px;\n      font-weight: 500;\n    }\n  }\n}\n\n.main-content {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 40px 24px;\n}\n\n// 分析来源区域\n.analysis-source {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n// 立体文档区域\n.document-section {\n  margin-bottom: 32px;\n\n  .document-label {\n    font-size: 14px;\n    color: #666;\n    margin-bottom: 12px;\n    font-weight: 500;\n  }\n\n  .document-content {\n    background: #f8f9fa;\n    border: 1px solid #e8e8e8;\n    border-radius: 6px;\n    padding: 16px;\n    min-height: 120px;\n\n    .document-text {\n      font-size: 14px;\n      line-height: 1.6;\n      color: #333;\n      margin-bottom: 12px;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n    }\n  }\n}\n\n// 选择关联词区域\n.related-words-section {\n  .section-header {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    margin-bottom: 16px;\n\n    .section-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n    }\n\n    .word-count {\n      font-size: 14px;\n      color: #999;\n    }\n  }\n\n  .words-container {\n    .add-word-btn {\n      display: inline-flex;\n      align-items: center;\n      gap: 6px;\n      padding: 8px 16px;\n      background: #f0f7ff;\n      color: #5470c6;\n      border: 1px dashed #5470c6;\n      border-radius: 4px;\n      font-size: 14px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      margin-bottom: 12px;\n\n      &:hover {\n        background: #e6f4ff;\n        border-color: #4096ff;\n      }\n\n      i {\n        font-size: 12px;\n      }\n    }\n\n    .word-description {\n      font-size: 12px;\n      color: #999;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 底部按钮区域\n.bottom-actions {\n  text-align: center;\n  padding-top: 24px;\n\n  .el-button {\n    padding: 12px 32px;\n    font-size: 16px;\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .steps-container {\n    gap: 30px;\n    padding: 16px 0;\n\n    .step-item {\n      font-size: 13px;\n    }\n  }\n\n  .main-content {\n    padding: 24px 16px;\n  }\n\n  .analysis-source {\n    padding: 24px 20px;\n  }\n\n  .document-content {\n    padding: 12px;\n    min-height: 100px;\n  }\n}\n</style>\n"]}]}