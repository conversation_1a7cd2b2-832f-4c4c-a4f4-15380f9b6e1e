<template>
  <div class="opinion-analysis">
    <!-- 步骤指示器 -->
    <div class="steps-container">
      <div class="step-item active">
        <span class="step-number">1</span>
        <span class="step-text">舆情分析来源</span>
      </div>
      <div class="step-item">
        <span class="step-number">2</span>
        <span class="step-text">数据概览</span>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 分析来源区域 -->
      <div class="analysis-source">
        <h2 class="section-title">分析来源</h2>

        <!-- 立体文档区域 -->
        <div class="document-section">
          <div class="document-label">立体文档</div>
          <div class="document-content">
            <div class="document-text">
              中国人工智能的发展历程可以分为四个阶段，即萌芽期、发展期、产业化、人工智能等。
            </div>
            <div class="document-text">
              当前正在快速发展阶段中国人工智能产业，例如，分析已突破传统行业的应用边界，在金融、医疗等传统领域（工业/制造业）
            </div>
          </div>
        </div>

        <!-- 选择关联词区域 -->
        <div class="related-words-section">
          <div class="section-header">
            <span class="section-label">选择关联词</span>
            <span class="word-count">(0/)</span>
          </div>

          <div class="words-container">
            <div class="add-word-btn">
              <i class="el-icon-plus"></i>
              <span>立体文档</span>
            </div>
            <div class="word-description">
              将根据选定的关联词对上述工作进行立体文档
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮区域 -->
      <div class="bottom-actions">
        <el-button type="primary" size="large">下一步</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OpinionAnalysis',
  data() {
    return {
      // 页面数据将在这里定义
    }
  },
  mounted() {
    // 页面初始化逻辑
    console.log('舆情分析页面已加载')
  },
  methods: {
    // 页面方法将在这里定义
  }
}
</script>

<style lang="scss" scoped>
.opinion-analysis {
  padding: 0;
  background-color: #f8f9fa;
  min-height: 100vh;
}

// 步骤指示器样式
.steps-container {
  background: white;
  padding: 20px 0;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: center;
  gap: 60px;

  .step-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #999;
    font-size: 14px;

    &.active {
      color: #5470c6;
      font-weight: 500;

      .step-number {
        background: #5470c6;
        color: white;
      }
    }

    .step-number {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #e8e8e8;
      color: #999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 500;
    }
  }
}

.main-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 24px;
}

// 分析来源区域
.analysis-source {
  background: white;
  border-radius: 8px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 32px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 24px 0;
  }
}

// 立体文档区域
.document-section {
  margin-bottom: 32px;

  .document-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
    font-weight: 500;
  }

  .document-content {
    background: #f8f9fa;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    padding: 16px;
    min-height: 120px;

    .document-text {
      font-size: 14px;
      line-height: 1.6;
      color: #333;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 选择关联词区域
.related-words-section {
  .section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;

    .section-label {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }

    .word-count {
      font-size: 14px;
      color: #999;
    }
  }

  .words-container {
    .add-word-btn {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      padding: 8px 16px;
      background: #f0f7ff;
      color: #5470c6;
      border: 1px dashed #5470c6;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 12px;

      &:hover {
        background: #e6f4ff;
        border-color: #4096ff;
      }

      i {
        font-size: 12px;
      }
    }

    .word-description {
      font-size: 12px;
      color: #999;
      line-height: 1.5;
    }
  }
}

// 底部按钮区域
.bottom-actions {
  text-align: center;
  padding-top: 24px;

  .el-button {
    padding: 12px 32px;
    font-size: 16px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .steps-container {
    gap: 30px;
    padding: 16px 0;

    .step-item {
      font-size: 13px;
    }
  }

  .main-content {
    padding: 24px 16px;
  }

  .analysis-source {
    padding: 24px 20px;
  }

  .document-content {
    padding: 12px;
    min-height: 100px;
  }
}
</style>
