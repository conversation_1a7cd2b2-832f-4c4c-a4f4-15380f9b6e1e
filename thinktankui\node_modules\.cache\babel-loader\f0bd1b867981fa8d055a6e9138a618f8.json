{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751521318873}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\babel.config.js", "mtime": 1750933247176}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "searchText", "activeMenuItem", "viewMode", "timeRange", "customDateRange", "analysisTypeMap", "mounted", "console", "log", "initializeData", "methods", "endDate", "Date", "startDate", "setDate", "getDate", "handleSearch", "handleMenuSelect", "index", "loadAnalysisData", "getCurrentAnalysisName", "handleDateChange", "analysisType", "handleCreateAnalysis", "$message", "info", "handleExportReport", "handleRefreshData", "success", "handleSettings"], "sources": ["src/views/opinion-analysis/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 顶部操作按钮区域 -->\n    <div class=\"action-buttons\">\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleCreateAnalysis\">新建分析</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"handleExportReport\">导出报告</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-refresh\" @click=\"handleRefreshData\">刷新数据</el-button>\n      <el-button type=\"primary\" icon=\"el-icon-setting\" @click=\"handleSettings\">分析设置</el-button>\n    </div>\n\n    <div class=\"opinion-analysis-container\">\n      <!-- 左侧导航栏 -->\n      <div class=\"left-sidebar\">\n        <div class=\"sidebar-header\">\n          <span class=\"sidebar-title\">舆情分析</span>\n          <i class=\"el-icon-data-analysis\"></i>\n        </div>\n\n        <div class=\"sidebar-search\">\n          <el-input\n            v-model=\"searchText\"\n            placeholder=\"搜索分析项目\"\n            size=\"small\"\n            prefix-icon=\"el-icon-search\"\n            @input=\"handleSearch\">\n          </el-input>\n        </div>\n\n        <div class=\"sidebar-menu\">\n          <div class=\"menu-section\">\n            <div class=\"section-title\">分析类型</div>\n            <el-menu\n              :default-active=\"activeMenuItem\"\n              class=\"sidebar-menu-list\"\n              @select=\"handleMenuSelect\">\n              <el-menu-item index=\"emotion\">\n                <i class=\"el-icon-sunny\"></i>\n                <span>情感分析</span>\n              </el-menu-item>\n              <el-menu-item index=\"trend\">\n                <i class=\"el-icon-trend-charts\"></i>\n                <span>趋势分析</span>\n              </el-menu-item>\n              <el-menu-item index=\"regional\">\n                <i class=\"el-icon-location\"></i>\n                <span>地域分析</span>\n              </el-menu-item>\n              <el-menu-item index=\"keyword\">\n                <i class=\"el-icon-key\"></i>\n                <span>关键词分析</span>\n              </el-menu-item>\n              <el-menu-item index=\"influence\">\n                <i class=\"el-icon-star-on\"></i>\n                <span>影响力评估</span>\n              </el-menu-item>\n              <el-menu-item index=\"spread\">\n                <i class=\"el-icon-share\"></i>\n                <span>传播路径</span>\n              </el-menu-item>\n            </el-menu>\n          </div>\n        </div>\n      </div>\n\n      <!-- 右侧内容区域 -->\n      <div class=\"right-content\">\n        <!-- 内容头部 -->\n        <div class=\"content-header\">\n          <div class=\"analysis-title\">\n            <span class=\"analysis-name\">{{ getCurrentAnalysisName() }}</span>\n            <i class=\"el-icon-arrow-right\"></i>\n          </div>\n          <div class=\"view-actions\">\n            <el-button-group>\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-s-grid\" :class=\"{ 'is-active': viewMode === 'grid' }\" @click=\"viewMode = 'grid'\"></el-button>\n              <el-button size=\"small\" icon=\"el-icon-menu\" :class=\"{ 'is-active': viewMode === 'list' }\" @click=\"viewMode = 'list'\"></el-button>\n              <el-button size=\"small\" icon=\"el-icon-s-unfold\" :class=\"{ 'is-active': viewMode === 'detail' }\" @click=\"viewMode = 'detail'\"></el-button>\n            </el-button-group>\n          </div>\n        </div>\n\n        <!-- 时间筛选区域 -->\n        <div class=\"filter-section\">\n          <div class=\"filter-row\">\n            <span class=\"filter-label\">时间范围:</span>\n            <el-radio-group v-model=\"timeRange\" size=\"small\">\n              <el-radio-button label=\"today\">今天</el-radio-button>\n              <el-radio-button label=\"week\">最近7天</el-radio-button>\n              <el-radio-button label=\"month\">最近30天</el-radio-button>\n              <el-radio-button label=\"custom\">自定义</el-radio-button>\n            </el-radio-group>\n\n            <el-date-picker\n              v-if=\"timeRange === 'custom'\"\n              v-model=\"customDateRange\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n              size=\"small\"\n              style=\"margin-left: 10px;\"\n              @change=\"handleDateChange\">\n            </el-date-picker>\n          </div>\n        </div>\n\n        <!-- 主要内容区域 -->\n        <div class=\"main-content\">\n          <!-- 根据选中的分析类型显示不同内容 -->\n          <div v-if=\"activeMenuItem === 'emotion'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-sunny\"></i>情感分析</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-sunny\"></i>\n                </div>\n                <h4>情感分析功能</h4>\n                <p>智能识别文本情感倾向，分析正面、负面、中性情绪分布</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n\n          <div v-else-if=\"activeMenuItem === 'trend'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-trend-charts\"></i>趋势分析</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-trend-charts\"></i>\n                </div>\n                <h4>趋势分析功能</h4>\n                <p>追踪舆情发展趋势，预测未来走向和热度变化</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n\n          <div v-else-if=\"activeMenuItem === 'regional'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-location\"></i>地域分析</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-location\"></i>\n                </div>\n                <h4>地域分析功能</h4>\n                <p>分析不同地区的舆情分布特征和地域差异</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n\n          <div v-else-if=\"activeMenuItem === 'keyword'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-key\"></i>关键词分析</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-key\"></i>\n                </div>\n                <h4>关键词分析功能</h4>\n                <p>提取热门关键词，分析词频和关联性</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n\n          <div v-else-if=\"activeMenuItem === 'influence'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-star-on\"></i>影响力评估</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-star-on\"></i>\n                </div>\n                <h4>影响力评估功能</h4>\n                <p>评估舆情事件的影响力和传播效果</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n\n          <div v-else-if=\"activeMenuItem === 'spread'\" class=\"analysis-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3><i class=\"el-icon-share\"></i>传播路径</h3>\n                <el-button type=\"text\" size=\"small\">查看详情</el-button>\n              </div>\n              <div class=\"analysis-placeholder\">\n                <div class=\"placeholder-icon\">\n                  <i class=\"el-icon-share\"></i>\n                </div>\n                <h4>传播路径分析功能</h4>\n                <p>追踪信息传播路径，分析传播节点和影响范围</p>\n                <el-button type=\"primary\" size=\"small\">开始分析</el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      // 搜索文本\n      searchText: '',\n      // 当前激活的菜单项\n      activeMenuItem: 'emotion',\n      // 视图模式\n      viewMode: 'grid',\n      // 时间范围\n      timeRange: 'week',\n      // 自定义时间范围\n      customDateRange: null,\n      // 分析类型映射\n      analysisTypeMap: {\n        'emotion': '情感分析',\n        'trend': '趋势分析',\n        'regional': '地域分析',\n        'keyword': '关键词分析',\n        'influence': '影响力评估',\n        'spread': '传播路径'\n      }\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n    this.initializeData()\n  },\n  methods: {\n    // 初始化数据\n    initializeData() {\n      // 设置默认时间范围为最近7天\n      const endDate = new Date()\n      const startDate = new Date()\n      startDate.setDate(startDate.getDate() - 7)\n      this.customDateRange = [startDate, endDate]\n    },\n\n    // 处理搜索\n    handleSearch() {\n      console.log('搜索:', this.searchText)\n      // 这里可以添加搜索逻辑\n    },\n\n    // 处理菜单选择\n    handleMenuSelect(index) {\n      this.activeMenuItem = index\n      console.log('选择分析类型:', index)\n      // 根据选择的分析类型加载对应数据\n      this.loadAnalysisData(index)\n    },\n\n    // 获取当前分析名称\n    getCurrentAnalysisName() {\n      return this.analysisTypeMap[this.activeMenuItem] || '舆情分析'\n    },\n\n    // 处理时间范围变化\n    handleDateChange() {\n      console.log('时间范围变化:', this.customDateRange)\n      this.loadAnalysisData(this.activeMenuItem)\n    },\n\n    // 加载分析数据\n    loadAnalysisData(analysisType) {\n      console.log('加载分析数据:', analysisType)\n      // 这里可以调用API获取对应的分析数据\n    },\n\n    // 顶部操作按钮事件\n    handleCreateAnalysis() {\n      this.$message.info('新建分析功能开发中')\n    },\n\n    handleExportReport() {\n      this.$message.info('导出报告功能开发中')\n    },\n\n    handleRefreshData() {\n      this.$message.success('数据刷新成功')\n      this.loadAnalysisData(this.activeMenuItem)\n    },\n\n    handleSettings() {\n      this.$message.info('分析设置功能开发中')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n// 主容器样式\n.app-container {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n// 顶部操作按钮区域\n.action-buttons {\n  background-color: #fff;\n  padding: 16px 24px;\n  border-bottom: 1px solid #e8e8e8;\n  margin-bottom: 0;\n\n  .el-button {\n    margin-right: 12px;\n\n    &:last-child {\n      margin-right: 0;\n    }\n  }\n}\n\n// 主要容器布局\n.opinion-analysis-container {\n  display: flex;\n  height: calc(100vh - 120px);\n  background-color: #f5f5f5;\n}\n\n// 左侧边栏样式\n.left-sidebar {\n  width: 280px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n\n  .sidebar-header {\n    padding: 20px 16px;\n    border-bottom: 1px solid #e8e8e8;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .sidebar-title {\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n    }\n\n    i {\n      font-size: 18px;\n      color: #666;\n    }\n  }\n\n  .sidebar-search {\n    padding: 16px;\n    border-bottom: 1px solid #f0f0f0;\n  }\n\n  .sidebar-menu {\n    flex: 1;\n    padding: 16px;\n\n    .section-title {\n      font-size: 14px;\n      color: #666;\n      margin-bottom: 12px;\n      font-weight: 500;\n    }\n\n    .sidebar-menu-list {\n      border: none;\n\n      .el-menu-item {\n        height: 44px;\n        line-height: 44px;\n        margin-bottom: 4px;\n        border-radius: 6px;\n        color: #666;\n\n        &:hover {\n          background-color: #f5f5f5;\n          color: #333;\n        }\n\n        &.is-active {\n          background-color: #e6f7ff;\n          color: #1890ff;\n\n          i {\n            color: #1890ff;\n          }\n        }\n\n        i {\n          margin-right: 8px;\n          font-size: 16px;\n        }\n      }\n    }\n  }\n}\n\n// 右侧内容区域\n.right-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: #f5f5f5;\n\n  .content-header {\n    background-color: #fff;\n    padding: 16px 24px;\n    border-bottom: 1px solid #e8e8e8;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .analysis-title {\n      display: flex;\n      align-items: center;\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n\n      .analysis-name {\n        margin-right: 8px;\n      }\n\n      i {\n        color: #999;\n        font-size: 14px;\n      }\n    }\n\n    .view-actions {\n      .el-button-group {\n        .el-button {\n          &.is-active {\n            background-color: #1890ff;\n            border-color: #1890ff;\n            color: #fff;\n          }\n        }\n      }\n    }\n  }\n\n  .filter-section {\n    background-color: #fff;\n    padding: 16px 24px;\n    border-bottom: 1px solid #e8e8e8;\n\n    .filter-row {\n      display: flex;\n      align-items: center;\n      flex-wrap: wrap;\n\n      .filter-label {\n        font-size: 14px;\n        color: #666;\n        margin-right: 12px;\n        white-space: nowrap;\n      }\n\n      .el-radio-group {\n        margin-right: 16px;\n      }\n    }\n  }\n\n  .main-content {\n    flex: 1;\n    padding: 24px;\n    overflow-y: auto;\n  }\n}\n\n// 分析内容区域\n.analysis-content {\n  .content-card {\n    background-color: #fff;\n    border-radius: 8px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    overflow: hidden;\n\n    .card-header {\n      padding: 20px 24px;\n      border-bottom: 1px solid #f0f0f0;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      h3 {\n        margin: 0;\n        font-size: 16px;\n        font-weight: 600;\n        color: #333;\n        display: flex;\n        align-items: center;\n\n        i {\n          margin-right: 8px;\n          font-size: 18px;\n          color: #1890ff;\n        }\n      }\n    }\n\n    .analysis-placeholder {\n      padding: 80px 40px;\n      text-align: center;\n\n      .placeholder-icon {\n        margin-bottom: 24px;\n\n        i {\n          font-size: 64px;\n          color: #d9d9d9;\n        }\n      }\n\n      h4 {\n        font-size: 18px;\n        color: #333;\n        margin: 0 0 12px 0;\n        font-weight: 600;\n      }\n\n      p {\n        font-size: 14px;\n        color: #666;\n        margin: 0 0 32px 0;\n        line-height: 1.6;\n      }\n\n      .el-button {\n        padding: 10px 24px;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 1200px) {\n  .left-sidebar {\n    width: 240px;\n  }\n}\n\n@media (max-width: 768px) {\n  .opinion-analysis-container {\n    flex-direction: column;\n    height: auto;\n  }\n\n  .left-sidebar {\n    width: 100%;\n    height: auto;\n    border-right: none;\n    border-bottom: 1px solid #e8e8e8;\n  }\n\n  .right-content {\n    .main-content {\n      padding: 16px;\n    }\n  }\n\n  .action-buttons {\n    padding: 12px 16px;\n\n    .el-button {\n      margin-bottom: 8px;\n      margin-right: 8px;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;iCAyNA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,UAAA;MACA;MACAC,cAAA;MACA;MACAC,QAAA;MACA;MACAC,SAAA;MACA;MACAC,eAAA;MACA;MACAC,eAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACAC,OAAA,CAAAC,GAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA;IACAD,cAAA,WAAAA,eAAA;MACA;MACA,IAAAE,OAAA,OAAAC,IAAA;MACA,IAAAC,SAAA,OAAAD,IAAA;MACAC,SAAA,CAAAC,OAAA,CAAAD,SAAA,CAAAE,OAAA;MACA,KAAAX,eAAA,IAAAS,SAAA,EAAAF,OAAA;IACA;IAEA;IACAK,YAAA,WAAAA,aAAA;MACAT,OAAA,CAAAC,GAAA,aAAAR,UAAA;MACA;IACA;IAEA;IACAiB,gBAAA,WAAAA,iBAAAC,KAAA;MACA,KAAAjB,cAAA,GAAAiB,KAAA;MACAX,OAAA,CAAAC,GAAA,YAAAU,KAAA;MACA;MACA,KAAAC,gBAAA,CAAAD,KAAA;IACA;IAEA;IACAE,sBAAA,WAAAA,uBAAA;MACA,YAAAf,eAAA,MAAAJ,cAAA;IACA;IAEA;IACAoB,gBAAA,WAAAA,iBAAA;MACAd,OAAA,CAAAC,GAAA,iBAAAJ,eAAA;MACA,KAAAe,gBAAA,MAAAlB,cAAA;IACA;IAEA;IACAkB,gBAAA,WAAAA,iBAAAG,YAAA;MACAf,OAAA,CAAAC,GAAA,YAAAc,YAAA;MACA;IACA;IAEA;IACAC,oBAAA,WAAAA,qBAAA;MACA,KAAAC,QAAA,CAAAC,IAAA;IACA;IAEAC,kBAAA,WAAAA,mBAAA;MACA,KAAAF,QAAA,CAAAC,IAAA;IACA;IAEAE,iBAAA,WAAAA,kBAAA;MACA,KAAAH,QAAA,CAAAI,OAAA;MACA,KAAAT,gBAAA,MAAAlB,cAAA;IACA;IAEA4B,cAAA,WAAAA,eAAA;MACA,KAAAL,QAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}